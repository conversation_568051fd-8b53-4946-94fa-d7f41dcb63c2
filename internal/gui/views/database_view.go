// Package views provides the database module UI with Material Design 3 and cyberpunk styling
package views

import (
	"fmt"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// DatabaseView represents the database module interface
type DatabaseView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// Database components
	connectionList   *widget.List
	queryEditor      *widget.Entry
	resultTable      *widget.Table
	statusLabel      *widget.Label
	connectionStatus *widget.Label

	// Current state
	connections       []DatabaseConnection
	currentDB         string
	queryResult       [][]string
	selectedConnection int
}

// DatabaseConnection represents a database connection
type DatabaseConnection struct {
	Name     string
	Host     string
	Port     string
	Database string
	Username string
	Status   string
}

// NewDatabaseView creates a new database module view
func NewDatabaseView(cfg *config.Config, theme *cybertheme.CyberTheme) (*DatabaseView, error) {
	view := &DatabaseView{
		config: cfg,
		theme:  theme,
		connections: []DatabaseConnection{
			{Name: "Local PostgreSQL", Host: "localhost", Port: "5432", Database: "postgres", Username: "postgres", Status: "Disconnected"},
			{Name: "Development DB", Host: "dev.example.com", Port: "5432", Database: "app_dev", Username: "dev_user", Status: "Disconnected"},
			{Name: "Production DB", Host: "prod.example.com", Port: "5432", Database: "app_prod", Username: "prod_user", Status: "Disconnected"},
		},
		queryResult: [][]string{
			{"ID", "Name", "Email", "Created"},
			{"1", "John Doe", "<EMAIL>", "2024-01-15"},
			{"2", "Jane Smith", "<EMAIL>", "2024-01-16"},
			{"3", "Bob Wilson", "<EMAIL>", "2024-01-17"},
		},
	}

	// Create the main content
	content := view.createDatabaseContent()

	// Create scrollable container
	scrollContainer := container.NewScroll(content)
	scrollContainer.SetMinSize(fyne.NewSize(900, 650))

	view.container = scrollContainer
	view.content = content

	return view, nil
}

// createDatabaseContent creates the main database interface
func (dv *DatabaseView) createDatabaseContent() fyne.CanvasObject {
	// Create header
	header := widget.NewRichTextFromMarkdown(`
# 🗄️ Database Management Module

**PostgreSQL Navigator & Query Interface**

---
`)

	// Create connection management section
	connectionSection := dv.createConnectionSection()

	// Create query interface section
	querySection := dv.createQuerySection()

	// Create results section
	resultsSection := dv.createResultsSection()

	// Create status bar
	statusSection := dv.createStatusSection()

	// Combine all sections with proper spacing
	return container.NewVBox(
		header,
		widget.NewSeparator(),
		connectionSection,
		widget.NewSeparator(),
		querySection,
		widget.NewSeparator(),
		resultsSection,
		widget.NewSeparator(),
		statusSection,
	)
}

// createConnectionSection creates the database connection management interface
func (dv *DatabaseView) createConnectionSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Database Connections")
	sectionHeader.TextStyle.Bold = true

	// Connection list
	dv.connectionList = widget.NewList(
		func() int { return len(dv.connections) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil),
				widget.NewLabel("Connection"),
				widget.NewLabel("Status"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			conn := dv.connections[id]
			hbox := obj.(*fyne.Container)

			// Status icon
			statusIcon := "🔴"
			if conn.Status == "Connected" {
				statusIcon = "🟢"
			}

			// Update labels
			hbox.Objects[1].(*widget.Label).SetText(fmt.Sprintf("%s (%s:%s)", conn.Name, conn.Host, conn.Port))
			hbox.Objects[2].(*widget.Label).SetText(fmt.Sprintf("%s %s", statusIcon, conn.Status))
		},
	)
	dv.connectionList.Resize(fyne.NewSize(400, 150))

	// Set up selection handling
	dv.connectionList.OnSelected = func(id widget.ListItemID) {
		dv.selectedConnection = id
	}

	// Connection buttons
	connectBtn := createEnhancedButton("Connect", func() {
		dv.connectToDatabase()
	}, dv.theme, true)

	disconnectBtn := createEnhancedButton("Disconnect", func() {
		dv.disconnectFromDatabase()
	}, dv.theme, false)

	addBtn := createEnhancedButton("Add Connection", func() {
		dv.showAddConnectionDialog()
	}, dv.theme, false)

	buttonContainer := container.NewHBox(connectBtn, disconnectBtn, addBtn)

	// Connection status
	dv.connectionStatus = widget.NewLabel("No active connections")
	dv.connectionStatus.TextStyle.Bold = true

	return container.NewVBox(
		sectionHeader,
		dv.connectionList,
		buttonContainer,
		dv.connectionStatus,
	)
}

// createQuerySection creates the SQL query interface
func (dv *DatabaseView) createQuerySection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("SQL Query Interface")
	sectionHeader.TextStyle.Bold = true

	// Query editor
	dv.queryEditor = widget.NewMultiLineEntry()
	dv.queryEditor.SetPlaceHolder("Enter your SQL query here...")
	dv.queryEditor.SetText("SELECT * FROM users LIMIT 10;")
	dv.queryEditor.Resize(fyne.NewSize(800, 120))

	// Query buttons
	executeBtn := createEnhancedButton("Execute Query", func() {
		dv.executeQuery()
	}, dv.theme, true)

	explainBtn := createEnhancedButton("Explain Plan", func() {
		dv.explainQuery()
	}, dv.theme, false)

	saveBtn := createEnhancedButton("Save Query", func() {
		dv.saveQuery()
	}, dv.theme, false)

	queryButtons := container.NewHBox(executeBtn, explainBtn, saveBtn)

	return container.NewVBox(
		sectionHeader,
		dv.queryEditor,
		queryButtons,
	)
}

// createResultsSection creates the query results display
func (dv *DatabaseView) createResultsSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Query Results")
	sectionHeader.TextStyle.Bold = true

	// Results table
	dv.resultTable = widget.NewTable(
		func() (int, int) { return len(dv.queryResult), len(dv.queryResult[0]) },
		func() fyne.CanvasObject { return widget.NewLabel("Cell") },
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)
			if id.Row < len(dv.queryResult) && id.Col < len(dv.queryResult[id.Row]) {
				label.SetText(dv.queryResult[id.Row][id.Col])
				if id.Row == 0 {
					label.TextStyle.Bold = true
				}
			}
		},
	)
	dv.resultTable.Resize(fyne.NewSize(800, 200))

	// Export buttons
	exportCSVBtn := createEnhancedButton("Export CSV", func() {
		dv.exportResults("csv")
	}, dv.theme, false)

	exportJSONBtn := createEnhancedButton("Export JSON", func() {
		dv.exportResults("json")
	}, dv.theme, false)

	exportButtons := container.NewHBox(exportCSVBtn, exportJSONBtn)

	return container.NewVBox(
		sectionHeader,
		dv.resultTable,
		exportButtons,
	)
}

// createStatusSection creates the status display
func (dv *DatabaseView) createStatusSection() fyne.CanvasObject {
	dv.statusLabel = widget.NewLabel(">>> DATABASE MODULE READY <<<")
	dv.statusLabel.Alignment = fyne.TextAlignCenter
	dv.statusLabel.TextStyle.Bold = true

	return dv.statusLabel
}

// Database operation methods
func (dv *DatabaseView) connectToDatabase() {
	if dv.selectedConnection >= 0 && dv.selectedConnection < len(dv.connections) {
		dv.connections[dv.selectedConnection].Status = "Connected"
		dv.connectionStatus.SetText(fmt.Sprintf("Connected to: %s", dv.connections[dv.selectedConnection].Name))
		dv.statusLabel.SetText(">>> DATABASE CONNECTED <<<")
		dv.connectionList.Refresh()
	}
}

func (dv *DatabaseView) disconnectFromDatabase() {
	if dv.selectedConnection >= 0 && dv.selectedConnection < len(dv.connections) {
		dv.connections[dv.selectedConnection].Status = "Disconnected"
		dv.connectionStatus.SetText("No active connections")
		dv.statusLabel.SetText(">>> DATABASE DISCONNECTED <<<")
		dv.connectionList.Refresh()
	}
}

func (dv *DatabaseView) executeQuery() {
	query := strings.TrimSpace(dv.queryEditor.Text)
	if query != "" {
		dv.statusLabel.SetText(">>> EXECUTING QUERY <<<")
		// Simulate query execution
		dv.resultTable.Refresh()
		dv.statusLabel.SetText(">>> QUERY EXECUTED SUCCESSFULLY <<<")
	}
}

func (dv *DatabaseView) explainQuery() {
	dv.statusLabel.SetText(">>> GENERATING EXPLAIN PLAN <<<")
	// TODO: Implement explain plan functionality
}

func (dv *DatabaseView) saveQuery() {
	dv.statusLabel.SetText(">>> QUERY SAVED <<<")
	// TODO: Implement query saving functionality
}

func (dv *DatabaseView) exportResults(format string) {
	dv.statusLabel.SetText(fmt.Sprintf(">>> EXPORTING RESULTS AS %s <<<", strings.ToUpper(format)))
	// TODO: Implement export functionality
}

func (dv *DatabaseView) showAddConnectionDialog() {
	dv.statusLabel.SetText(">>> ADD CONNECTION DIALOG <<<")
	// TODO: Implement add connection dialog
}

// Content returns the database view's content
func (dv *DatabaseView) Content() fyne.CanvasObject {
	return dv.container
}

// Refresh refreshes the database view
func (dv *DatabaseView) Refresh() {
	if dv.container != nil {
		dv.container.Refresh()
	}
}
