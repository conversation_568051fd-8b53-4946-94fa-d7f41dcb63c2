// Package views provides the sidebar navigation component
// Fixes text cutoff issues and implements proper cyberpunk styling
package views

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// Sidebar represents the navigation sidebar
type Sidebar struct {
	config           *config.Config
	theme            *cybertheme.CyberTheme
	container        *container.Scroll
	moduleContainer  *fyne.Container
	onModuleSelected func(string)

	// Module buttons for tracking selection
	moduleButtons map[string]*widget.Button
	selectedModule string
}

// ModuleInfo represents information about a module for the sidebar
type ModuleInfo struct {
	Name        string
	DisplayName string
	Icon        fyne.Resource
	Enabled     bool
	Status      string
}

// NewSidebar creates an enhanced sidebar with improved text display and Material Design 3 styling
func NewSidebar(cfg *config.Config, theme *cybertheme.CyberTheme, onModuleSelected func(string)) (*Sidebar, error) {
	// Create module container with enhanced spacing
	moduleContainer := container.NewVBox()

	// Create scrollable container with improved sizing to fix text cutoff issues
	scrollContainer := container.NewScroll(moduleContainer)
	scrollContainer.SetMinSize(fyne.NewSize(300, 600)) // Further increased width for better text visibility and spacing

	sidebar := &Sidebar{
		config:           cfg,
		theme:            theme,
		container:        scrollContainer,
		moduleContainer:  moduleContainer,
		onModuleSelected: onModuleSelected,
		moduleButtons:    make(map[string]*widget.Button),
	}

	// Initialize with default modules
	sidebar.initializeModules()

	return sidebar, nil
}

// initializeModules sets up the default module list
func (s *Sidebar) initializeModules() {
	// Create header
	header := widget.NewRichTextFromMarkdown(`
# 🖥️ MODULES

**DEVELOPMENT ASSISTANT**
`)
	s.moduleContainer.Add(header)
	s.moduleContainer.Add(widget.NewSeparator())

	// Define default modules based on Architecture.md
	modules := []ModuleInfo{
		{
			Name:        "database",
			DisplayName: "🗄️ Database",
			Enabled:     true,
			Status:      "Ready",
		},
		{
			Name:        "kubernetes",
			DisplayName: "☸️ Kubernetes",
			Enabled:     true,
			Status:      "Ready",
		},
		{
			Name:        "tasks",
			DisplayName: "📋 Tasks",
			Enabled:     true,
			Status:      "Ready",
		},
		{
			Name:        "mcp",
			DisplayName: "🤖 MCP",
			Enabled:     true,
			Status:      "Ready",
		},
		{
			Name:        "docker",
			DisplayName: "🐳 Docker",
			Enabled:     false,
			Status:      "Planned",
		},
		{
			Name:        "golang",
			DisplayName: "🔧 Go Tools",
			Enabled:     false,
			Status:      "Planned",
		},
		{
			Name:        "search",
			DisplayName: "🔍 Search",
			Enabled:     false,
			Status:      "Planned",
		},
		{
			Name:        "ai",
			DisplayName: "🧠 AI Agent",
			Enabled:     false,
			Status:      "Future",
		},
	}

	// Add modules to sidebar
	for _, module := range modules {
		s.addModuleButton(module)
	}

	// Add footer
	s.moduleContainer.Add(widget.NewSeparator())
	footer := widget.NewLabel(">>> CYBER TERMINAL <<<")
	s.moduleContainer.Add(footer)
}

// addModuleButton creates and adds an enhanced module button to the sidebar
func (s *Sidebar) addModuleButton(module ModuleInfo) {
	// Create button with proper text formatting
	buttonText := fmt.Sprintf("%s\n[%s]", module.DisplayName, module.Status)

	// Create standard button for now (will enhance later)
	button := widget.NewButton(buttonText, func() {
		s.selectModule(module.Name)
	})

	// Set minimum size to ensure text is fully visible and meets touch targets
	button.Resize(fyne.NewSize(260, 80)) // Further increased size for better readability and spacing

	// Apply styling based on module status
	if !module.Enabled {
		button.Disable()
	} else {
		button.Importance = widget.HighImportance
	}

	// Store button reference
	s.moduleButtons[module.Name] = button

	// Create enhanced separator
	separator := widget.NewSeparator()

	// Create container for the button with enhanced spacing
	spacer := widget.NewLabel("") // Vertical spacer for better separation
	buttonContainer := container.NewVBox(
		button,
		spacer,
		separator,
		spacer, // Additional spacer after separator
	)

	s.moduleContainer.Add(buttonContainer)
}

// selectModule handles module selection
func (s *Sidebar) selectModule(moduleName string) {
	// Update visual selection
	s.updateSelection(moduleName)

	// Call the callback
	if s.onModuleSelected != nil {
		s.onModuleSelected(moduleName)
	}
}

// updateSelection updates the visual selection state
func (s *Sidebar) updateSelection(selectedModule string) {
	// Reset all buttons to normal state
	for name, button := range s.moduleButtons {
		if name == s.selectedModule {
			// Reset previous selection
			button.Importance = widget.MediumImportance
		}
	}

	// Highlight selected button
	if button, exists := s.moduleButtons[selectedModule]; exists {
		button.Importance = widget.HighImportance
		s.selectedModule = selectedModule
	}

	// Refresh all buttons
	for _, button := range s.moduleButtons {
		button.Refresh()
	}
}

// AddModule adds a new module to the sidebar (for dynamic modules)
func (s *Sidebar) AddModule(moduleUI interface{}) {
	// This will be implemented when we have the ModuleUI interface
	// For now, it's a placeholder for future module registration
}

// Content returns the sidebar's content
func (s *Sidebar) Content() fyne.CanvasObject {
	return s.container
}

// Refresh refreshes the sidebar
func (s *Sidebar) Refresh() {
	if s.container != nil {
		s.container.Refresh()
	}
	if s.moduleContainer != nil {
		s.moduleContainer.Refresh()
	}
}

// SetModuleStatus updates the status of a module
func (s *Sidebar) SetModuleStatus(moduleName, status string) {
	if button, exists := s.moduleButtons[moduleName]; exists {
		// Find the module info and update button text
		// This is a simplified implementation - in a full version,
		// we'd store module info and update accordingly
		currentText := button.Text
		// Extract display name (before newline)
		if idx := len(currentText); idx > 0 {
			displayName := currentText
			if newlineIdx := 0; newlineIdx < len(currentText) {
				for i, char := range currentText {
					if char == '\n' {
						displayName = currentText[:i]
						break
					}
				}
			}
			newText := fmt.Sprintf("%s\n[%s]", displayName, status)
			button.SetText(newText)
		}
	}
}

// EnableModule enables or disables a module
func (s *Sidebar) EnableModule(moduleName string, enabled bool) {
	if button, exists := s.moduleButtons[moduleName]; exists {
		if enabled {
			button.Enable()
		} else {
			button.Disable()
		}
	}
}

// GetSelectedModule returns the currently selected module
func (s *Sidebar) GetSelectedModule() string {
	return s.selectedModule
}

// SetMinWidth sets the minimum width of the sidebar
func (s *Sidebar) SetMinWidth(width float32) {
	currentSize := s.container.MinSize()
	s.container.SetMinSize(fyne.NewSize(width, currentSize.Height))
}

// AddSeparator adds a separator to the sidebar
func (s *Sidebar) AddSeparator() {
	s.moduleContainer.Add(widget.NewSeparator())
}

// AddCustomWidget adds a custom widget to the sidebar
func (s *Sidebar) AddCustomWidget(widget fyne.CanvasObject) {
	s.moduleContainer.Add(widget)
}

// ShowConnectionStatus shows connection status for modules
func (s *Sidebar) ShowConnectionStatus(moduleName string, connected bool) {
	// This could be enhanced to show connection indicators
	status := "Disconnected"
	if connected {
		status = "Connected"
	}
	s.SetModuleStatus(moduleName, status)
}
