// Package views provides GUI view components for Assistant-Go
// Implements the cyberpunk aesthetic with proper scrolling and responsive design
package views

import (
	"fmt"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// MainView represents the main content area of the application
type MainView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// Module views
	databaseView   *DatabaseView
	kubernetesView *KubernetesView
	tasksView      *TasksView
	mcpView        *MCPView
	dockerView     *DockerView
	searchView     *SearchView
	chatView       *ChatView
	currentModule  string
}

// NewMainView creates a new main view with cyberpunk styling and module support
func NewMainView(cfg *config.Config, theme *cybertheme.CyberTheme) (*MainView, error) {
	// Create the main content container with scrolling support
	// This fixes the content overflow issues mentioned in requirements
	mainContainer := container.NewVBox()

	// Create scrollable container to handle content overflow with enhanced responsive design
	scrollContainer := container.NewScroll(mainContainer)
	// Set responsive minimum size that adapts to different screen sizes
	scrollContainer.SetMinSize(fyne.NewSize(600, 400))
	// Enable both horizontal and vertical scrolling for complete content overflow handling
	scrollContainer.Direction = container.ScrollBoth

	// Create welcome content with cyberpunk styling
	welcomeContent := createWelcomeContent(cfg, theme)
	mainContainer.Add(welcomeContent)

	// Initialize module views
	databaseView, err := NewDatabaseView(cfg, theme)
	if err != nil {
		return nil, fmt.Errorf("failed to create database view: %w", err)
	}

	kubernetesView, err := NewKubernetesView(cfg, theme)
	if err != nil {
		return nil, fmt.Errorf("failed to create kubernetes view: %w", err)
	}

	tasksView, err := NewTasksView(cfg, theme)
	if err != nil {
		return nil, fmt.Errorf("failed to create tasks view: %w", err)
	}

	mcpView, err := NewMCPView(cfg, theme)
	if err != nil {
		return nil, fmt.Errorf("failed to create mcp view: %w", err)
	}

	return &MainView{
		config:         cfg,
		theme:          theme,
		container:      scrollContainer,
		content:        welcomeContent,
		databaseView:   databaseView,
		kubernetesView: kubernetesView,
		tasksView:      tasksView,
		mcpView:        mcpView,
		currentModule:  "welcome",
	}, nil
}

// createWelcomeContent creates the initial welcome screen
func createWelcomeContent(cfg *config.Config, theme *cybertheme.CyberTheme) fyne.CanvasObject {
	// Create cyberpunk-styled welcome message
	welcomeText := widget.NewRichTextFromMarkdown(`
# 🖥️ Assistant-Go Development Environment

**STATUS:** SYSTEM ONLINE
**VERSION:** ` + cfg.App.Version + `
**ENVIRONMENT:** ` + cfg.App.Environment + `

> Welcome to your cyberpunk development assistant.
> All systems are operational and ready for your commands.

## Available Modules:

### 🗄️ Database Module
- **PostgreSQL Navigator** - Intelligent query optimization and visual explain plans
- **Connection Management** - Multiple database connections with auto-pooling
- **AI-Powered Analysis** - Smart error explanations and performance suggestions

### ☸️ Kubernetes Module
- **Cluster Commander** - Visual cluster management and real-time monitoring
- **Resource Browser** - Intuitive navigation of namespaces, pods, and services
- **Deployment Tools** - Drag-and-drop resource management

### 📋 Task Module
- **Makefile Integration** - Direct access to your build tasks
- **Taskfile Support** - Modern task runner integration
- **Favorites Management** - Quick access to frequently used commands

### 🤖 MCP Module (Future)
- **Model Context Protocol** - Visualize AI interactions in real-time
- **Context Flow** - Debug and understand AI tool calls
- **Response Analysis** - Monitor model responses and context usage

## Getting Started:

1. **Configure Connections** - Set up your database and Kubernetes connections
2. **Select a Module** - Choose from the sidebar to begin working
3. **Explore Features** - Each module provides comprehensive development tools

---

**>>> READY FOR DEVELOPMENT <<<**
`)

	// Create enhanced status indicators with optimal spacing
	statusContainer := container.NewHBox(
		createEnhancedStatusIndicator("Database", "offline", theme),
		createEnhancedStatusIndicator("Kubernetes", "offline", theme),
		createEnhancedStatusIndicator("Tasks", "online", theme),
		createEnhancedStatusIndicator("MCP", "pending", theme),
	)

	// Create enhanced action buttons with optimal spacing
	actionContainer := container.NewHBox(
		createEnhancedButton("Configure Database", func() {
			// TODO: Open database configuration
		}, theme, true),
		createEnhancedButton("Setup Kubernetes", func() {
			// TODO: Open K8s configuration
		}, theme, false),
		createEnhancedButton("View Tasks", func() {
			// TODO: Switch to tasks module
		}, theme, false),
	)

	// Create enhanced separators
	separator1 := widget.NewSeparator()
	separator2 := widget.NewSeparator()
	separator3 := widget.NewSeparator()

	// Create section headers with enhanced typography
	statusHeader := widget.NewLabel("System Status:")
	statusHeader.TextStyle.Bold = true

	actionsHeader := widget.NewLabel("Quick Actions:")
	actionsHeader.TextStyle.Bold = true

	footerLabel := widget.NewLabel(">>> ENHANCED CYBERPUNK DEVELOPMENT ENVIRONMENT ACTIVE <<<")
	footerLabel.Alignment = fyne.TextAlignCenter

	// Add spacing widgets for better layout
	spacer1 := widget.NewLabel("") // Vertical spacer
	spacer2 := widget.NewLabel("") // Vertical spacer
	spacer3 := widget.NewLabel("") // Vertical spacer
	spacer4 := widget.NewLabel("") // Vertical spacer

	// Combine all elements with significantly improved spacing and visual hierarchy
	return container.NewVBox(
		welcomeText,
		spacer1,
		separator1,
		spacer2,
		statusHeader,
		statusContainer,
		spacer3,
		separator2,
		spacer4,
		actionsHeader,
		actionContainer,
		separator3,
		footerLabel,
	)
}

// createEnhancedStatusIndicator creates an enhanced status indicator with better styling
func createEnhancedStatusIndicator(name, status string, theme *cybertheme.CyberTheme) fyne.CanvasObject {
	// Create status label with enhanced styling
	statusLabel := widget.NewLabel(strings.ToUpper(status))
	statusLabel.TextStyle.Bold = true

	// Create name label
	nameLabel := widget.NewLabel(name)
	nameLabel.TextStyle.Bold = true

	// Create status icon based on status
	var statusIcon string
	switch status {
	case "online":
		statusIcon = "🟢"
	case "offline":
		statusIcon = "🔴"
	case "pending":
		statusIcon = "🟡"
	default:
		statusIcon = "⚪"
	}

	iconLabel := widget.NewLabel(statusIcon)

	return container.NewVBox(
		nameLabel,
		container.NewHBox(iconLabel, statusLabel),
	)
}

// createEnhancedButton creates an enhanced button with improved styling and text visibility
func createEnhancedButton(text string, tapped func(), theme *cybertheme.CyberTheme, primary bool) *widget.Button {
	// Use the universal button creation function for consistent styling
	button := cybertheme.CreateStandardButton(text, tapped, theme)

	// Set importance based on type
	if primary {
		button.Importance = widget.HighImportance
	} else {
		button.Importance = widget.HighImportance // Force high importance for better visibility
	}

	// Set optimal size for better touch targets and text visibility
	button.Resize(fyne.NewSize(180, 50))

	return button
}

// Content returns the main view's content
func (mv *MainView) Content() fyne.CanvasObject {
	return mv.container
}

// SetContent updates the main view's content with enhanced scrolling support
func (mv *MainView) SetContent(content fyne.CanvasObject) {
	// Clear existing content
	mv.container.Content = container.NewVBox()

	// Add new content with proper scrolling support
	if content != nil {
		// Wrap content in a container that supports proper scrolling
		contentContainer := container.NewVBox(content)
		mv.container.Content = contentContainer
		mv.content = content

		// Ensure scrolling is enabled for content overflow
		mv.container.Direction = container.ScrollBoth
	}

	// Refresh the container
	mv.container.Refresh()
}

// Refresh refreshes the main view
func (mv *MainView) Refresh() {
	if mv.container != nil {
		mv.container.Refresh()
	}
}

// GetCurrentContent returns the current content
func (mv *MainView) GetCurrentContent() fyne.CanvasObject {
	return mv.content
}

// SetMinSize sets the minimum size of the main view
func (mv *MainView) SetMinSize(size fyne.Size) {
	mv.container.SetMinSize(size)
}

// ShowLoading displays a loading indicator
func (mv *MainView) ShowLoading(message string) {
	loadingContent := container.NewVBox(
		widget.NewProgressBarInfinite(),
		widget.NewLabel(message),
		widget.NewLabel(">>> PROCESSING <<<"),
	)

	mv.SetContent(loadingContent)
}

// ShowError displays an error message with cyberpunk styling
func (mv *MainView) ShowError(title, message string) {
	errorContent := container.NewVBox(
		widget.NewLabel("🚨 ERROR DETECTED 🚨"),
		widget.NewSeparator(),
		widget.NewLabel("ERROR: "+title),
		widget.NewLabel(message),
		widget.NewSeparator(),
		widget.NewLabel(">>> SYSTEM ALERT <<<"),
	)

	mv.SetContent(errorContent)
}

// ShowSuccess displays a success message
func (mv *MainView) ShowSuccess(title, message string) {
	successContent := container.NewVBox(
		widget.NewLabel("✅ OPERATION SUCCESSFUL ✅"),
		widget.NewSeparator(),
		widget.NewLabel("SUCCESS: "+title),
		widget.NewLabel(message),
		widget.NewSeparator(),
		widget.NewLabel(">>> TASK COMPLETED <<<"),
	)

	mv.SetContent(successContent)
}

// SwitchToModule switches the main view to display the specified module
func (mv *MainView) SwitchToModule(moduleName string) error {
	mv.currentModule = moduleName

	switch moduleName {
	case "database":
		if mv.databaseView != nil {
			mv.SetContent(mv.databaseView.Content())
			return nil
		}
		return fmt.Errorf("database view not initialized")

	case "kubernetes":
		if mv.kubernetesView != nil {
			mv.SetContent(mv.kubernetesView.Content())
			return nil
		}
		return fmt.Errorf("kubernetes view not initialized")

	case "tasks":
		if mv.tasksView != nil {
			mv.SetContent(mv.tasksView.Content())
			return nil
		}
		return fmt.Errorf("tasks view not initialized")

	case "mcp":
		if mv.mcpView != nil {
			mv.SetContent(mv.mcpView.Content())
			return nil
		}
		return fmt.Errorf("mcp view not initialized")

	case "welcome":
		welcomeContent := createWelcomeContent(mv.config, mv.theme)
		mv.SetContent(welcomeContent)
		return nil

	default:
		return fmt.Errorf("unknown module: %s", moduleName)
	}
}

// GetCurrentModule returns the currently active module
func (mv *MainView) GetCurrentModule() string {
	return mv.currentModule
}

// RefreshCurrentModule refreshes the currently active module
func (mv *MainView) RefreshCurrentModule() {
	switch mv.currentModule {
	case "database":
		if mv.databaseView != nil {
			mv.databaseView.Refresh()
		}
	case "kubernetes":
		if mv.kubernetesView != nil {
			mv.kubernetesView.Refresh()
		}
	case "tasks":
		if mv.tasksView != nil {
			mv.tasksView.Refresh()
		}
	case "mcp":
		if mv.mcpView != nil {
			mv.mcpView.Refresh()
		}
	}
}
