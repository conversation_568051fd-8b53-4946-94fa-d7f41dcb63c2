// Package views provides the Search module UI with Material Design 3 and cyberpunk styling
package views

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// SearchView represents the Search module interface
type SearchView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// Search components
	searchEntry    *widget.Entry
	searchTypeSelect *widget.Select
	resultsList    *widget.List
	resultViewer   *widget.RichText
	statusLabel    *widget.Label
	searchHistory  *widget.List

	// Current state
	searchResults []SearchResult
	searchQueries []SearchQuery
	selectedResult int
	currentQuery   string
	searchType     string
}

// SearchResult represents a search result
type SearchResult struct {
	Title       string
	URL         string
	Description string
	Source      string
	Relevance   float64
	Timestamp   time.Time
}

// SearchQuery represents a search query
type SearchQuery struct {
	Query     string
	Type      string
	Results   int
	Timestamp time.Time
}

// NewSearchView creates a new Search module view
func NewSearchView(cfg *config.Config, theme *cybertheme.CyberTheme) (*SearchView, error) {
	view := &SearchView{
		config: cfg,
		theme:  theme,
		searchResults: []SearchResult{
			{Title: "Go Documentation - Getting Started", URL: "https://golang.org/doc/", Description: "Official Go programming language documentation and tutorials", Source: "golang.org", Relevance: 0.95, Timestamp: time.Now()},
			{Title: "Fyne GUI Framework", URL: "https://fyne.io/", Description: "Cross-platform GUI toolkit for Go applications", Source: "fyne.io", Relevance: 0.90, Timestamp: time.Now()},
			{Title: "Docker Documentation", URL: "https://docs.docker.com/", Description: "Complete Docker documentation and guides", Source: "docker.com", Relevance: 0.85, Timestamp: time.Now()},
		},
		searchQueries: []SearchQuery{
			{Query: "golang fyne gui", Type: "Web", Results: 156, Timestamp: time.Now().Add(-10 * time.Minute)},
			{Query: "docker container management", Type: "Documentation", Results: 89, Timestamp: time.Now().Add(-5 * time.Minute)},
			{Query: "kubernetes deployment", Type: "Code", Results: 234, Timestamp: time.Now().Add(-2 * time.Minute)},
		},
		selectedResult: -1,
		searchType:     "Web",
	}

	// Create main content
	content := view.createContent()

	// Create scrollable container with enhanced responsive design
	scrollContainer := container.NewScroll(content)
	scrollContainer.SetMinSize(fyne.NewSize(600, 400))
	scrollContainer.Direction = container.ScrollBoth

	view.container = scrollContainer
	view.content = content

	return view, nil
}

// createContent creates the main Search interface
func (sv *SearchView) createContent() fyne.CanvasObject {
	// Create header
	header := widget.NewRichTextFromMarkdown(`
# 🔍 Search Engine Integration

**SearXNG & Multi-Source Search**

---
`)

	// Create search interface section
	searchSection := sv.createSearchSection()

	// Create results section
	resultsSection := sv.createResultsSection()

	// Create history section
	historySection := sv.createHistorySection()

	// Create status section
	statusSection := sv.createStatusSection()

	// Combine all sections with proper spacing
	return container.NewVBox(
		header,
		widget.NewSeparator(),
		searchSection,
		widget.NewSeparator(),
		resultsSection,
		widget.NewSeparator(),
		historySection,
		widget.NewSeparator(),
		statusSection,
	)
}

// createSearchSection creates the search interface
func (sv *SearchView) createSearchSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Search Interface")
	sectionHeader.TextStyle.Bold = true

	// Search entry
	sv.searchEntry = widget.NewEntry()
	sv.searchEntry.SetPlaceHolder("Enter search query...")
	sv.searchEntry.Resize(fyne.NewSize(600, 40))

	// Search type selector
	sv.searchTypeSelect = widget.NewSelect([]string{"Web", "Documentation", "Code", "Images", "Videos"}, func(value string) {
		sv.searchType = value
		sv.statusLabel.SetText(fmt.Sprintf(">>> SEARCH TYPE: %s <<<", value))
	})
	sv.searchTypeSelect.SetSelected("Web")

	// Search buttons with enhanced styling
	searchBtn := cybertheme.CreateStandardButton("Search", func() {
		sv.performSearch()
	}, sv.theme)

	clearBtn := cybertheme.CreateStandardButton("Clear", func() {
		sv.clearSearch()
	}, sv.theme)

	advancedBtn := cybertheme.CreateStandardButton("Advanced", func() {
		sv.showAdvancedSearch()
	}, sv.theme)

	searchButtons := container.NewHBox(searchBtn, clearBtn, advancedBtn)

	// Search options
	optionsLabel := widget.NewLabel("Search Options:")
	optionsLabel.TextStyle.Bold = true

	safeSearchCheck := widget.NewCheck("Safe Search", func(checked bool) {
		sv.statusLabel.SetText(fmt.Sprintf(">>> SAFE SEARCH: %t <<<", checked))
	})

	instantCheck := widget.NewCheck("Instant Results", func(checked bool) {
		sv.statusLabel.SetText(fmt.Sprintf(">>> INSTANT RESULTS: %t <<<", checked))
	})

	optionsContainer := container.NewHBox(safeSearchCheck, instantCheck)

	return container.NewVBox(
		sectionHeader,
		sv.searchEntry,
		container.NewHBox(
			widget.NewLabel("Type:"),
			sv.searchTypeSelect,
		),
		searchButtons,
		optionsLabel,
		optionsContainer,
	)
}

// createResultsSection creates the search results interface
func (sv *SearchView) createResultsSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Search Results")
	sectionHeader.TextStyle.Bold = true

	// Results list
	sv.resultsList = widget.NewList(
		func() int { return len(sv.searchResults) },
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Title"),
				widget.NewLabel("URL"),
				widget.NewLabel("Description"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			result := sv.searchResults[id]
			vbox := obj.(*fyne.Container)

			// Update labels
			vbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("📄 %s", result.Title))
			vbox.Objects[0].(*widget.Label).TextStyle.Bold = true
			vbox.Objects[1].(*widget.Label).SetText(fmt.Sprintf("🔗 %s", result.URL))
			vbox.Objects[2].(*widget.Label).SetText(result.Description)
		},
	)
	sv.resultsList.Resize(fyne.NewSize(800, 200))

	// Set up selection handling
	sv.resultsList.OnSelected = func(id widget.ListItemID) {
		sv.selectedResult = id
		sv.showResultDetails()
	}

	// Result action buttons with enhanced styling
	openBtn := cybertheme.CreateStandardButton("Open", func() {
		sv.openResult()
	}, sv.theme)

	copyBtn := cybertheme.CreateStandardButton("Copy URL", func() {
		sv.copyResultURL()
	}, sv.theme)

	saveBtn := cybertheme.CreateStandardButton("Save", func() {
		sv.saveResult()
	}, sv.theme)

	resultButtons := container.NewHBox(openBtn, copyBtn, saveBtn)

	// Result viewer
	viewerLabel := widget.NewLabel("Result Details:")
	viewerLabel.TextStyle.Bold = true

	sv.resultViewer = widget.NewRichText()
	sv.resultViewer.SetText("Select a search result to view details...")
	sv.resultViewer.Resize(fyne.NewSize(800, 100))

	return container.NewVBox(
		sectionHeader,
		sv.resultsList,
		resultButtons,
		viewerLabel,
		sv.resultViewer,
	)
}

// createHistorySection creates the search history interface
func (sv *SearchView) createHistorySection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Search History")
	sectionHeader.TextStyle.Bold = true

	// History list
	sv.searchHistory = widget.NewList(
		func() int { return len(sv.searchQueries) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Query"),
				widget.NewLabel("Type"),
				widget.NewLabel("Results"),
				widget.NewLabel("Time"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			query := sv.searchQueries[id]
			hbox := obj.(*fyne.Container)

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(query.Query)
			hbox.Objects[1].(*widget.Label).SetText(query.Type)
			hbox.Objects[2].(*widget.Label).SetText(fmt.Sprintf("%d results", query.Results))
			hbox.Objects[3].(*widget.Label).SetText(query.Timestamp.Format("15:04"))
		},
	)
	sv.searchHistory.Resize(fyne.NewSize(800, 120))

	// History action buttons with enhanced styling
	repeatBtn := cybertheme.CreateStandardButton("Repeat", func() {
		sv.repeatSearch()
	}, sv.theme)

	clearHistoryBtn := cybertheme.CreateStandardButton("Clear History", func() {
		sv.clearHistory()
	}, sv.theme)

	historyButtons := container.NewHBox(repeatBtn, clearHistoryBtn)

	return container.NewVBox(
		sectionHeader,
		sv.searchHistory,
		historyButtons,
	)
}

// createStatusSection creates the status display
func (sv *SearchView) createStatusSection() fyne.CanvasObject {
	sv.statusLabel = widget.NewLabel(">>> SEARCH MODULE READY <<<")
	sv.statusLabel.Alignment = fyne.TextAlignCenter
	sv.statusLabel.TextStyle.Bold = true

	return sv.statusLabel
}

// Search operation methods
func (sv *SearchView) performSearch() {
	query := strings.TrimSpace(sv.searchEntry.Text)
	if query != "" {
		sv.currentQuery = query
		sv.statusLabel.SetText(">>> SEARCHING <<<")

		// Add to history
		newQuery := SearchQuery{
			Query:     query,
			Type:      sv.searchType,
			Results:   len(sv.searchResults),
			Timestamp: time.Now(),
		}
		sv.searchQueries = append([]SearchQuery{newQuery}, sv.searchQueries...)

		sv.searchHistory.Refresh()
		sv.statusLabel.SetText(fmt.Sprintf(">>> FOUND %d RESULTS <<<", len(sv.searchResults)))
	}
}

func (sv *SearchView) clearSearch() {
	sv.searchEntry.SetText("")
	sv.resultViewer.SetText("Search cleared...")
	sv.statusLabel.SetText(">>> SEARCH CLEARED <<<")
}

func (sv *SearchView) showAdvancedSearch() {
	sv.statusLabel.SetText(">>> ADVANCED SEARCH OPTIONS <<<")
	// TODO: Implement advanced search dialog
}

func (sv *SearchView) showResultDetails() {
	if sv.selectedResult >= 0 && sv.selectedResult < len(sv.searchResults) {
		result := sv.searchResults[sv.selectedResult]
		details := fmt.Sprintf(`**%s**

**URL:** %s
**Source:** %s
**Relevance:** %.2f
**Timestamp:** %s

**Description:**
%s`, result.Title, result.URL, result.Source, result.Relevance, result.Timestamp.Format("2006-01-02 15:04:05"), result.Description)

		sv.resultViewer.ParseMarkdown(details)
		sv.statusLabel.SetText(">>> VIEWING RESULT DETAILS <<<")
	}
}

func (sv *SearchView) openResult() {
	if sv.selectedResult >= 0 && sv.selectedResult < len(sv.searchResults) {
		result := sv.searchResults[sv.selectedResult]
		sv.statusLabel.SetText(fmt.Sprintf(">>> OPENING: %s <<<", result.URL))
		// TODO: Implement URL opening
	}
}

func (sv *SearchView) copyResultURL() {
	if sv.selectedResult >= 0 && sv.selectedResult < len(sv.searchResults) {
		result := sv.searchResults[sv.selectedResult]
		sv.statusLabel.SetText(">>> URL COPIED TO CLIPBOARD <<<")
		// TODO: Implement clipboard copy
	}
}

func (sv *SearchView) saveResult() {
	sv.statusLabel.SetText(">>> RESULT SAVED <<<")
	// TODO: Implement result saving
}

func (sv *SearchView) repeatSearch() {
	sv.statusLabel.SetText(">>> REPEATING SEARCH <<<")
	// TODO: Implement search repeat from history
}

func (sv *SearchView) clearHistory() {
	sv.searchQueries = []SearchQuery{}
	sv.searchHistory.Refresh()
	sv.statusLabel.SetText(">>> SEARCH HISTORY CLEARED <<<")
}

// Content returns the Search view's content
func (sv *SearchView) Content() fyne.CanvasObject {
	return sv.container
}

// Refresh refreshes the Search view
func (sv *SearchView) Refresh() {
	if sv.container != nil {
		sv.container.Refresh()
	}
}

