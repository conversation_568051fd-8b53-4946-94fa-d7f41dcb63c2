// Package views provides the Chat module UI with Material Design 3 and cyberpunk styling
package views

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// ChatView represents the Chat module interface for AI assistant integration
type ChatView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// Chat components
	messageList     *widget.List
	messageEntry    *widget.Entry
	modelSelect     *widget.Select
	conversationList *widget.List
	contextViewer   *widget.RichText
	statusLabel     *widget.Label
	aiStatus        *widget.Label

	// Current state
	messages       []ChatMessage
	conversations  []Conversation
	currentModel   string
	currentConversation string
	selectedMessage int
	isTyping       bool
}

// ChatMessage represents a chat message
type ChatMessage struct {
	ID        string
	Content   string
	Role      string // "user", "assistant", "system"
	Model     string
	Timestamp time.Time
	Tokens    int
}

// Conversation represents a chat conversation
type Conversation struct {
	ID          string
	Title       string
	Model       string
	Messages    int
	Created     time.Time
	LastMessage time.Time
}

// NewChatView creates a new Chat module view
func NewChatView(cfg *config.Config, theme *cybertheme.CyberTheme) (*ChatView, error) {
	view := &ChatView{
		config: cfg,
		theme:  theme,
		messages: []ChatMessage{
			{ID: "msg001", Content: "Hello! I'm your AI development assistant. How can I help you today?", Role: "assistant", Model: "Claude-3", Timestamp: time.Now().Add(-5 * time.Minute), Tokens: 15},
			{ID: "msg002", Content: "Can you help me optimize this Go code for better performance?", Role: "user", Model: "", Timestamp: time.Now().Add(-4 * time.Minute), Tokens: 12},
			{ID: "msg003", Content: "I'd be happy to help optimize your Go code! Please share the code you'd like me to review, and I'll analyze it for performance improvements, memory efficiency, and best practices.", Role: "assistant", Model: "Claude-3", Timestamp: time.Now().Add(-3 * time.Minute), Tokens: 35},
		},
		conversations: []Conversation{
			{ID: "conv001", Title: "Go Performance Optimization", Model: "Claude-3", Messages: 8, Created: time.Now().Add(-2 * time.Hour), LastMessage: time.Now().Add(-3 * time.Minute)},
			{ID: "conv002", Title: "Docker Container Setup", Model: "Gemini-Pro", Messages: 12, Created: time.Now().Add(-1 * time.Hour), LastMessage: time.Now().Add(-30 * time.Minute)},
			{ID: "conv003", Title: "Kubernetes Deployment", Model: "Claude-3", Messages: 6, Created: time.Now().Add(-45 * time.Minute), LastMessage: time.Now().Add(-15 * time.Minute)},
		},
		selectedMessage: -1,
		currentModel:    "Claude-3",
		isTyping:        false,
	}

	// Create main content
	content := view.createContent()

	// Create scrollable container with enhanced responsive design
	scrollContainer := container.NewScroll(content)
	scrollContainer.SetMinSize(fyne.NewSize(600, 400))
	scrollContainer.Direction = container.ScrollBoth

	view.container = scrollContainer
	view.content = content

	return view, nil
}

// createContent creates the main Chat interface
func (cv *ChatView) createContent() fyne.CanvasObject {
	// Create header
	header := widget.NewRichTextFromMarkdown(`
# 🤖 AI Assistant Chat

**Claude AI & Gemini AI Integration with LangChain-Go**

---
`)

	// Create model selection section
	modelSection := cv.createModelSection()

	// Create conversation management section
	conversationSection := cv.createConversationSection()

	// Create chat interface section
	chatSection := cv.createChatSection()

	// Create context section
	contextSection := cv.createContextSection()

	// Create status section
	statusSection := cv.createStatusSection()

	// Combine all sections with proper spacing
	return container.NewVBox(
		header,
		widget.NewSeparator(),
		modelSection,
		widget.NewSeparator(),
		conversationSection,
		widget.NewSeparator(),
		chatSection,
		widget.NewSeparator(),
		contextSection,
		widget.NewSeparator(),
		statusSection,
	)
}

// createModelSection creates the AI model selection interface
func (cv *ChatView) createModelSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("AI Model Selection")
	sectionHeader.TextStyle.Bold = true

	// Model selector
	cv.modelSelect = widget.NewSelect([]string{"Claude-3", "Claude-3.5-Sonnet", "Gemini-Pro", "Gemini-1.5-Pro"}, func(value string) {
		cv.currentModel = value
		cv.statusLabel.SetText(fmt.Sprintf(">>> MODEL SELECTED: %s <<<", value))
	})
	cv.modelSelect.SetSelected("Claude-3")

	// Model info
	modelInfo := widget.NewRichText()
	modelInfo.ParseMarkdown(`**Current Model:** Claude-3
**Context Window:** 200K tokens
**Capabilities:** Code analysis, debugging, optimization
**Status:** Connected`)

	// Model action buttons with enhanced styling
	connectBtn := cybertheme.CreateStandardButton("Connect", func() {
		cv.connectModel()
	}, cv.theme)

	testBtn := cybertheme.CreateStandardButton("Test", func() {
		cv.testModel()
	}, cv.theme)

	configBtn := cybertheme.CreateStandardButton("Configure", func() {
		cv.configureModel()
	}, cv.theme)

	modelButtons := container.NewHBox(connectBtn, testBtn, configBtn)

	return container.NewVBox(
		sectionHeader,
		container.NewHBox(
			widget.NewLabel("Model:"),
			cv.modelSelect,
		),
		modelInfo,
		modelButtons,
	)
}

// createConversationSection creates the conversation management interface
func (cv *ChatView) createConversationSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Conversation Management")
	sectionHeader.TextStyle.Bold = true

	// Conversation list
	cv.conversationList = widget.NewList(
		func() int { return len(cv.conversations) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Title"),
				widget.NewLabel("Model"),
				widget.NewLabel("Messages"),
				widget.NewLabel("Last"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			conversation := cv.conversations[id]
			hbox := obj.(*fyne.Container)

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(conversation.Title)
			hbox.Objects[1].(*widget.Label).SetText(conversation.Model)
			hbox.Objects[2].(*widget.Label).SetText(fmt.Sprintf("%d msgs", conversation.Messages))
			hbox.Objects[3].(*widget.Label).SetText(conversation.LastMessage.Format("15:04"))
		},
	)
	cv.conversationList.Resize(fyne.NewSize(800, 120))

	// Conversation action buttons with enhanced styling
	newBtn := cybertheme.CreateStandardButton("New", func() {
		cv.newConversation()
	}, cv.theme)

	loadBtn := cybertheme.CreateStandardButton("Load", func() {
		cv.loadConversation()
	}, cv.theme)

	saveBtn := cybertheme.CreateStandardButton("Save", func() {
		cv.saveConversation()
	}, cv.theme)

	exportBtn := cybertheme.CreateStandardButton("Export", func() {
		cv.exportConversation()
	}, cv.theme)

	conversationButtons := container.NewHBox(newBtn, loadBtn, saveBtn, exportBtn)

	return container.NewVBox(
		sectionHeader,
		cv.conversationList,
		conversationButtons,
	)
}

// createChatSection creates the main chat interface
func (cv *ChatView) createChatSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Chat Interface")
	sectionHeader.TextStyle.Bold = true

	// Message list
	cv.messageList = widget.NewList(
		func() int { return len(cv.messages) },
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Role"),
				widget.NewLabel("Content"),
				widget.NewLabel("Info"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			message := cv.messages[id]
			vbox := obj.(*fyne.Container)

			// Role indicator with icon
			roleIcon := "👤"
			if message.Role == "assistant" {
				roleIcon = "🤖"
			} else if message.Role == "system" {
				roleIcon = "⚙️"
			}

			// Update labels
			vbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s %s", roleIcon, strings.ToUpper(message.Role)))
			vbox.Objects[0].(*widget.Label).TextStyle.Bold = true
			vbox.Objects[1].(*widget.Label).SetText(message.Content)
			vbox.Objects[2].(*widget.Label).SetText(fmt.Sprintf("%s | %d tokens | %s", message.Model, message.Tokens, message.Timestamp.Format("15:04:05")))
		},
	)
	cv.messageList.Resize(fyne.NewSize(800, 200))

	// Message input
	cv.messageEntry = widget.NewMultiLineEntry()
	cv.messageEntry.SetPlaceHolder("Type your message here...")
	cv.messageEntry.Resize(fyne.NewSize(600, 80))

	// Chat action buttons with enhanced styling
	sendBtn := cybertheme.CreateStandardButton("Send", func() {
		cv.sendMessage()
	}, cv.theme)

	clearBtn := cybertheme.CreateStandardButton("Clear", func() {
		cv.clearChat()
	}, cv.theme)

	stopBtn := cybertheme.CreateStandardButton("Stop", func() {
		cv.stopGeneration()
	}, cv.theme)

	chatButtons := container.NewHBox(sendBtn, clearBtn, stopBtn)

	return container.NewVBox(
		sectionHeader,
		cv.messageList,
		cv.messageEntry,
		chatButtons,
	)
}
